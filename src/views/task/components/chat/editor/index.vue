<template>
  <div class="max-h222px scroller">
    <div
      class="content"
      :class="{ 'has-content': hasContent }"
      :spellcheck="false"
      autocorrect="off"
      autocapitalize="off"
      translate="no"
      ref="contentRef"
      :data-placeholder="placeholder"
      @keydown="handleKeydown"
      contenteditable="true"
      @input="handleInput"
    >
      <!-- 
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @click="handleClick" -->
      <template v-for="item in content" :key="item.id">
        <template v-if="item.type === 'text'">
          {{ item.value }}
        </template>
        <AgentInput
          v-else-if="item.type === 'InputSlot'"
          v-model:value="item.value"
          :placeholder="item.props?.placeholder"
          :slot-data="item"
          :ref="el => setAgentInputRef(el, item.id)"
        />
        <AgentSelect
          v-else-if="item.type === 'SelectSlot'"
          v-model:value="item.value"
          :placeholder="item.props?.placeholder"
          :slot-data="item"
          :ref="el => setAgentInputRef(el, item.id)"
        />
        <!-- @navigate="handleNavigate" -->
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import AgentInput from './agentInput.vue'
import { randomUUID } from '@/utils/util'
import AgentSelect from './agentSelect.vue'

export interface PromptItemType {
  type: string
  id: string
  value: string
  props?: { placeholder: string; options?: string[] }
}

interface Props {
  value: string
  placeholder?: string
  agentPrompt?: string
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '给我布置一个任务（可以让我寻找海外商机、解析海外国家政策、跟踪海外行业新闻...）',
  agentPrompt: ''
})
const emit = defineEmits<{
  'update:value': [value: string] // v-model双向绑定事件
}>()

function handleInput(e) {
  // 处理换行
  const value = contentRef.value?.innerText.replace(/\n/g, '')
  emit('update:value', value || '')
}

const contentRef = ref<HTMLDivElement>()
const hasContent = ref(false)
const content = ref<PromptItemType[]>([])

// AgentInput组件引用管理
const agentInputRefs = ref<Map<string, any>>(new Map())

// 解析agentPrompt内容，生成组件
function parseAgentPrompt(text: string): PromptItemType[] {
  text = text.replace(/\n/g, '')
  const result: PromptItemType[] = []
  let lastIndex = 0

  // 匹配 {"type": ...} 这样的 JSON 结构
  const regex = /(\{"type"[^{}]*(?:\{[^{}]*\}[^{}]*)*\})/g

  let match
  while ((match = regex.exec(text)) !== null) {
    const [fullMatch] = match

    // 提取前面的文本
    const precedingText = text.slice(lastIndex, match.index)
    if (precedingText) {
      result.push({ type: 'text', value: precedingText, id: randomUUID() })
    }

    // 尝试解析 JSON
    try {
      const json = JSON.parse(fullMatch)

      // 如果有 options 字段，转成数组
      if (json.props?.options && typeof json.props.options === 'string') {
        json.props.options = json.props.options.split(',')
      }

      result.push({ ...json, value: json.value ? json.value : '', id: randomUUID() })
    } catch (e) {
      console.warn('JSON 解析失败:', fullMatch)
      result.push({ type: 'text', value: fullMatch, id: randomUUID() })
    }

    lastIndex = regex.lastIndex
  }

  // 添加最后剩余的文本
  const remainingText = text.slice(lastIndex)
  if (remainingText) {
    result.push({ type: 'text', value: remainingText, id: randomUUID() })
  }

  return result
}

onMounted(() => {
  content.value = parseAgentPrompt(props.agentPrompt)
})

// 设置AgentInput组件引用
function setAgentInputRef(el: any, id: string) {
  if (el) {
    agentInputRefs.value.set(id, el)
  } else {
    agentInputRefs.value.delete(id)
  }
}

// 处理AgentInput导航事件，直接切换到下一个输入框
function handleNavigate(direction: 'left' | 'right', slotData: PromptItemType) {
  console.log('direction: ', direction)
  console.log('slotData: ', slotData)
  // 获取所有InputSlot类型的项目
  const inputSlots = content.value.filter(item => item.type === 'InputSlot')
  const currentIndex = inputSlots.findIndex(item => item.id === slotData.id)

  if (currentIndex === -1) return

  let targetIndex = -1
  if (direction === 'left' && currentIndex > 0) {
    targetIndex = currentIndex - 1
  } else if (direction === 'right' && currentIndex < inputSlots.length - 1) {
    targetIndex = currentIndex + 1
  }

  if (targetIndex !== -1) {
    const targetSlot = inputSlots[targetIndex]
    const targetRef = agentInputRefs.value.get(targetSlot.id)

    if (targetRef && targetRef.inputRef) {
      // 使用nextTick确保DOM更新完成后再聚焦
      nextTick(() => {
        targetRef.inputRef?.focus()
      })
    }
  }
}

function handleKeydown(e: KeyboardEvent) {
  if (e.key === 'Enter') {
    e.preventDefault()
    console.log('回车触发', contentRef.value?.innerText)
  }
}
</script>

<style lang="less" scoped>
.scroller {
  display: flex !important;
  align-items: flex-start !important;
  font-family: monospace;
  line-height: 1.4;
  height: 100%;
  overflow-x: auto;
  position: relative;
  z-index: 0;
  overflow-anchor: none;
}

.content {
  @apply min-h62px  fs-16px;
  cursor: text;
  outline: none;
  width: 100%;
  white-space: normal;
  word-break: break-word;
  overflow-wrap: anywhere;
  flex-shrink: 1;
  tab-size: 2;
  line-height: 30px;

  .textWarper {
    display: inline-flex;
    flex-wrap: wrap;
    align-items: center;
    flex-direction: row;
  }

  &[data-placeholder]:not(.has-content) {
    &:empty,
    &:has(br:only-child) {
      &:before {
        content: attr(data-placeholder);
        color: #999;
        pointer-events: none;
      }
    }
  }
}
</style>
