<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-07-15 14:30:59
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-07-16 17:12:56
 * @FilePath: /global-intelligence-web/src/views/task/components/chat/agentInput/index.vue
 * @Description: 可编辑的输入框组件，支持光标位置保持和placeholder显示
-->
<template>
  <!-- 外层容器，不可编辑 -->
  <div class="agentInput" :data-key="slotData.id" contenteditable="false">
    <!-- 内层输入区域，可编辑，支持placeholder显示 -->
    <span
      :class="['input-content', !props.value ? 'show-placeholder' : '']"
      :data-placeholder="placeholder"
      ref="inputRef"
      contenteditable="true"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @keydown="handleKeydown"
    ></span>
  </div>
</template>

<script setup lang="ts">
import { useTemplateRef, watchEffect } from 'vue'
import type { PromptItemType } from './index.vue'

// 组件接收的属性定义
interface Props {
  value: string // 输入框的值
  placeholder?: string // 占位符文本
  slotData: PromptItemType // 插槽数据
}

const props = defineProps<Props>()

// 定义组件触发的事件
const emit = defineEmits<{
  'update:value': [value: string] // v-model双向绑定事件
  input: [value: string, slotData: PromptItemType] // 输入事件
  focus: [slotData: PromptItemType] // 获得焦点事件
  blur: [slotData: PromptItemType] // 失去焦点事件
  navigate: [direction: 'left' | 'right', slotData: PromptItemType] // 导航事件
}>()

// 输入框DOM引用
const inputRef = useTemplateRef('inputRef')

// 暴露给父组件的方法和属性
defineExpose({
  inputRef
})

// 监听value变化，同步更新输入框内容并保持光标位置
watchEffect(() => {
  if (!inputRef.value) {
    return
  }

  // 如果内容已经相同，不需要更新
  if (inputRef.value.innerText === props.value) {
    return
  }

  // 保存当前光标位置
  const selection = window.getSelection()
  let cursorPosition = 0

  // 获取当前光标位置
  if (selection && selection.rangeCount > 0) {
    const range = selection.getRangeAt(0)
    // 如果光标在当前元素内，保存位置
    if (inputRef.value.contains(range.startContainer)) {
      cursorPosition = range.startOffset
      // 如果光标在文本节点中，需要计算相对于元素开始的位置
      if (range.startContainer.nodeType === Node.TEXT_NODE) {
        const walker = document.createTreeWalker(inputRef.value, NodeFilter.SHOW_TEXT)
        let textOffset = 0
        let node
        while ((node = walker.nextNode())) {
          if (node === range.startContainer) {
            cursorPosition = textOffset + range.startOffset
            break
          }
          textOffset += node.textContent?.length || 0
        }
      }
    }
  }

  // 更新输入框内容
  inputRef.value.innerText = props.value

  // 恢复光标位置
  if (cursorPosition > 0 && props.value.length > 0) {
    const newSelection = window.getSelection()
    if (newSelection) {
      const range = document.createRange()
      const walker = document.createTreeWalker(inputRef.value, NodeFilter.SHOW_TEXT)

      let currentOffset = 0
      let targetNode: Node | null = inputRef.value.firstChild

      // 遍历文本节点找到光标应该在的位置
      let node
      while ((node = walker.nextNode())) {
        const nodeLength = node.textContent?.length || 0
        if (currentOffset + nodeLength >= cursorPosition) {
          targetNode = node as Node
          const offsetInNode = cursorPosition - currentOffset
          range.setStart(targetNode, Math.min(offsetInNode, nodeLength))
          range.setEnd(targetNode, Math.min(offsetInNode, nodeLength))
          break
        }
        currentOffset += nodeLength
      }

      // 如果没有找到合适的文本节点，设置到末尾
      if (!targetNode || targetNode === inputRef.value) {
        if (inputRef.value.firstChild) {
          const firstChild = inputRef.value.firstChild
          range.setStart(firstChild, Math.min(cursorPosition, firstChild.textContent?.length || 0))
          range.setEnd(firstChild, Math.min(cursorPosition, firstChild.textContent?.length || 0))
        } else {
          range.setStart(inputRef.value, 0)
          range.setEnd(inputRef.value, 0)
        }
      }

      // 应用新的光标位置
      range.collapse(true)
      newSelection.removeAllRanges()
      newSelection.addRange(range)
    }
  }
})

// 处理输入事件
function handleInput() {
  const value = inputRef.value?.innerHTML
  // 处理特殊情况：当只有一个br标签时视为空内容
  const textValue = value === '<br>' ? '' : inputRef.value?.innerText || ''
  emit('update:value', textValue)
  emit('input', textValue, props.slotData)
}

// 处理获得焦点事件
function handleFocus() {
  emit('focus', props.slotData)
}

// 处理失去焦点事件
function handleBlur() {
  // 失焦时检查并清理空内容
  if (inputRef.value) {
    const value = inputRef.value.innerText || ''
    if (value.trim() === '' || value === '\n') {
      inputRef.value.innerHTML = ''
      emit('update:value', '')
      emit('input', '', props.slotData)
    }
  }
  emit('blur', props.slotData)
}

// 处理键盘事件
function handleKeydown(e: KeyboardEvent) {
  if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0)
      const isAtStart = range.startOffset === 0
      const isAtEnd = range.startContainer.textContent
        ? range.startOffset === range.startContainer.textContent.length
        : false

      // 在空内容时，或在内容的最左/最右边时触发导航
      if (!props.value.trim() || (e.key === 'ArrowLeft' && isAtStart) || (e.key === 'ArrowRight' && isAtEnd)) {
        // e.preventDefault()
        emit('navigate', e.key === 'ArrowLeft' ? 'left' : 'right', props.slotData)
      }
    }
  }
}
</script>

<style scoped lang="less">
// 定义插槽样式变量
@slotRadius: 8px;
@slotBgColor: #cfc5f2;
@slotMargin: 4px;
@slotPaddingX: 6px;
@slotPaddingY: 2px;
@slotHeight: 26px;

// 输入框容器样式
.agentInput {
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
  height: @slotHeight;
  line-height: @slotHeight;
  margin: 0 @slotMargin;
  border-radius: @slotRadius;
  padding: @slotPaddingY @slotPaddingX;
  background: @slotBgColor;
  color: #6553ee;
  cursor: text;
  outline: none;
}

// 输入内容区域样式
.input-content {
  outline: none;
  min-width: 1px;
  color: #6553ee;

  // 只在空内容且有 show-placeholder 类时显示 placeholder
  &.show-placeholder:empty::before {
    content: attr(data-placeholder);
    color: #f4f0ff;
    pointer-events: none;
  }

  // 处理可能存在的 br 标签情况
  // &.show-placeholder:has(br:only-child)::before {
  //   content: attr(data-placeholder);
  //   color: #f4f0ff;
  //   pointer-events: none;
  // }
}

// 辅助元素样式
.widgetBuffer,
.slot-side-right,
.slot-side-left {
  vertical-align: text-top;
  height: @slotHeight;
  width: 0;
  display: inline;
}

// 暂时注释掉的样式
// .slot-side-left {
//   border-radius: @slotRadius 0 0 @slotRadius;
//   padding: @slotPaddingY 0 @slotPaddingY @slotPaddingX;
//   background: @slotBgColor;
//   margin-left: @slotMargin;
// }
// .slot-side-right {
//   border-radius: 0 @slotRadius @slotRadius 0;
//   padding: @slotPaddingY @slotPaddingX @slotPaddingY 0;
//   background: @slotBgColor;
//   margin-right: @slotMargin;
// }

// .inputSlot {
//   padding: @slotPaddingY 0;
// }
</style>
