<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-07-14 10:36:13
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-07-16 18:05:06
 * @FilePath: /global-intelligence-web/src/views/task/components/chat/index.vue
 * @Description: 
-->
<template>
  <div class="chatBox">
    <div class="border-[0.5px] w-full p-16px box-shadow relative rounded-2xl">
      <Editor v-model:value="value" :agentPrompt="agentPrompt" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import Editor from './editor/index.vue'
import { useRoute } from 'vue-router'

const value = ref('')

const route = useRoute()
const agentId = route.query.agentId as string

const agentList = [
  {
    name: '纯输入框',
    id: '1',
    query:
      '我公司是' +
      '{"type":"InputSlot","props":{"placeholder":"公司介绍"}}' +
      '，我负责销售' +
      '{"type":"InputSlot","props":{"placeholder":"产品、服务或解决方案"}}' +
      '，正在寻找' +
      '{"type":"InputSlot","props":{"placeholder":"国家"}}' +
      '和' +
      '{"type":"InputSlot","props":{"placeholder":"行业"}}' +
      '的机会，请根据情报数据网帮我洞察机会点，并对机会的价值进行评级。'
  },
  {
    name: '多选框',
    id: '2',
    query:
      '正在寻找' +
      '{"type":"SelectSlot","props":{"placeholder":"国家","options":"中国,美国,日本"}}' +
      '和' +
      '{"type":"SelectSlot","props":{"placeholder":"行业","options":"服装,美妆,彩妆"}}' +
      '的机会，请根据情报数据网帮我洞察机会点，并对机会的价值进行评级。'
  },
  {
    name: '组合输入',
    id: '3',
    query:
      '我公司是' +
      '{"type":"InputSlot","props":{"placeholder":"公司介绍"}}' +
      '，我负责销售' +
      '{"type":"InputSlot","props":{"placeholder":"产品、服务或解决方案"}}' +
      '，正在寻找' +
      '{"type":"SelectSlot","props":{"placeholder":"国家","options":"中国,美国,日本"}}' +
      '和' +
      '{"type":"SelectSlot","props":{"placeholder":"行业","options":"服装,美妆,彩妆"}}' +
      '的机会，请根据情报数据网帮我洞察机会点，并对机会的价值进行评级。'
  }
]


const agentPrompt = computed(() => {
  const agent = agentList.find(item => item.id === agentId)
  return agent ? agent.query : ''
})
</script>

<style scoped></style>
