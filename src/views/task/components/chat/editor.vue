<template>
  <div class="max-h222px scroller">
    <div
      class="content"
      :class="{ 'has-content': hasContent }"
      :spellcheck="false"
      autocorrect="off"
      autocapitalize="off"
      translate="no"
      ref="contentRef"
      :data-placeholder="placeholder"
      @keydown="handleKeydown"
    >
      <!-- contenteditable="false"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @click="handleClick" -->
      <template v-for="item in content" :key="item.id">
        <span v-if="item.type === 'text'" contenteditable="false" class="cursor-default">
          {{ item.value }}
        </span>
        <AgentInput
          v-else-if="item.type === 'InputSlot'"
          :key="item.id"
          v-model:value="item.value"
          :placeholder="item.props?.placeholder"
          :slot-data="item"
        />
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import AgentInput from './agentInput/index.vue'
import { randomUUID } from '@/utils/util'

export interface PromptItemType {
  type: string
  id: string
  value: string
  props?: { placeholder: string; options?: string[] }
}

interface Props {
  placeholder?: string
  agentPrompt?: string
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '给我布置一个任务（可以让我寻找海外商机、解析海外国家政策、跟踪海外行业新闻...）',
  agentPrompt: ''
})

const contentRef = ref<HTMLDivElement>()
const hasContent = ref(false)
const content = ref<PromptItemType[]>([])

// 解析agentPrompt内容，生成组件
function parseAgentPrompt(text: string): PromptItemType[] {
  text = text.replace(/\n/g, '')
  const result: PromptItemType[] = []
  let lastIndex = 0

  // 匹配 {"type": ...} 这样的 JSON 结构
  const regex = /(\{"type"[^{}]*(?:\{[^{}]*\}[^{}]*)*\})/g

  let match
  while ((match = regex.exec(text)) !== null) {
    const [fullMatch] = match

    // 提取前面的文本
    const precedingText = text.slice(lastIndex, match.index)
    if (precedingText) {
      result.push({ type: 'text', value: precedingText, id: randomUUID() })
    }

    // 尝试解析 JSON
    try {
      const json = JSON.parse(fullMatch)

      // 如果有 options 字段，转成数组
      if (json.props?.options && typeof json.props.options === 'string') {
        json.props.options = json.props.options.split(',')
      }

      result.push({ ...json, value: json.value ? json.value : '', id: randomUUID() })
    } catch (e) {
      console.warn('JSON 解析失败:', fullMatch)
      result.push({ type: 'text', value: fullMatch, id: randomUUID() })
    }

    lastIndex = regex.lastIndex
  }

  // 添加最后剩余的文本
  const remainingText = text.slice(lastIndex)
  if (remainingText) {
    result.push({ type: 'text', value: remainingText, id: randomUUID() })
  }

  return result
}

onMounted(() => {
  content.value = parseAgentPrompt(props.agentPrompt)
})

function handleKeydown(e: KeyboardEvent) {
  if (e.key === 'Enter') {
    e.preventDefault()
    console.log('回车触发', contentRef.value?.innerText)
  }

  // 监听左右按键
  if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
    // 获取当前选区
    const selection = window.getSelection()
    const range = selection?.getRangeAt(0)
    if (!range) return

    // 获取光标所在的元素
    const focusNode = range.startContainer

    if (focusNode.nodeType === Node.ELEMENT_NODE) {
      console.log('range: ', range, focusNode)
      console.log('dom节点')
      //   if (range.startOffset === 0) {
      //   }
    }
  }
}
</script>

<style lang="less" scoped>
.scroller {
  display: flex !important;
  align-items: flex-start !important;
  font-family: monospace;
  line-height: 1.4;
  height: 100%;
  overflow-x: auto;
  position: relative;
  z-index: 0;
  overflow-anchor: none;
}

.content {
  @apply min-h62px  fs-16px;
  outline: none;
  width: 100%;
  white-space: normal;
  word-break: break-word;
  overflow-wrap: anywhere;
  flex-shrink: 1;
  tab-size: 2;
  line-height: 30px;

  .textWarper {
    display: inline-flex;
    flex-wrap: wrap;
    align-items: center;
    flex-direction: row;
  }

  &[data-placeholder]:not(.has-content) {
    &:empty,
    &:has(br:only-child) {
      &:before {
        content: attr(data-placeholder);
        color: #999;
        pointer-events: none;
      }
    }
  }
}
</style>
